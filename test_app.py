#!/usr/bin/env python3
"""
Test script for Course Video Player
This script tests the basic functionality of the application.
"""

import unittest
import json
import tempfile
import os
from app import app, db
from models import Course, Module, Video, UserPreference

class CoursePlayerTestCase(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = app
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
    
    def tearDown(self):
        """Tear down test fixtures after each test method."""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_index_page(self):
        """Test that the main page loads correctly."""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Course Player', response.data)
    
    def test_get_courses_empty(self):
        """Test getting courses when none exist."""
        response = self.client.get('/api/courses')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data), 0)
    
    def test_create_course(self):
        """Test creating a course programmatically."""
        with self.app.app_context():
            course = Course(
                name="Test Course",
                description="A test course",
                path="/test/path"
            )
            db.session.add(course)
            db.session.commit()
            
            # Test API response
            response = self.client.get('/api/courses')
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['name'], "Test Course")
    
    def test_user_preferences(self):
        """Test user preferences API."""
        # Get default preferences
        response = self.client.get('/api/preferences')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('preferences', data)
        
        # Update preferences
        new_prefs = {
            'theme': 'dark',
            'playback_speed': 1.5,
            'volume': 0.7
        }
        response = self.client.post('/api/preferences',
                                  data=json.dumps(new_prefs),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 200)
        
        # Verify preferences were saved
        response = self.client.get('/api/preferences')
        data = json.loads(response.data)
        self.assertEqual(data['preferences']['theme'], 'dark')
        self.assertEqual(data['preferences']['playback_speed'], 1.5)
    
    def test_search_empty(self):
        """Test search with no results."""
        response = self.client.get('/api/search?q=nonexistent')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data['courses']), 0)
        self.assertEqual(len(data['videos']), 0)
    
    def test_scan_directory_invalid_path(self):
        """Test scanning an invalid directory."""
        response = self.client.post('/api/scan-directory',
                                  data=json.dumps({'path': '/invalid/path'}),
                                  content_type='application/json')
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_video_progress_tracking(self):
        """Test video progress tracking."""
        with self.app.app_context():
            # Create test data
            course = Course(name="Test Course", path="/test")
            db.session.add(course)
            db.session.commit()
            
            module = Module(name="Test Module", path="/test/module", course_id=course.id)
            db.session.add(module)
            db.session.commit()
            
            video = Video(
                name="Test Video",
                filename="test.mp4",
                path="/test/video.mp4",
                module_id=module.id,
                duration=3600  # 1 hour
            )
            db.session.add(video)
            db.session.commit()
            
            # Test progress update
            progress_data = {
                'video_id': video.id,
                'current_time': 1800,  # 30 minutes
                'completed': False
            }
            
            response = self.client.post(f'/api/videos/{video.id}/progress',
                                      data=json.dumps(progress_data),
                                      content_type='application/json')
            self.assertEqual(response.status_code, 200)
            
            # Verify progress was saved
            response = self.client.get(f'/api/videos/{video.id}')
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertIsNotNone(data['progress'])
            self.assertEqual(data['progress']['current_time'], 1800)
    
    def test_bookmarks(self):
        """Test bookmark functionality."""
        with self.app.app_context():
            # Create test data
            course = Course(name="Test Course", path="/test")
            db.session.add(course)
            db.session.commit()
            
            module = Module(name="Test Module", path="/test/module", course_id=course.id)
            db.session.add(module)
            db.session.commit()
            
            video = Video(
                name="Test Video",
                filename="test.mp4",
                path="/test/video.mp4",
                module_id=module.id
            )
            db.session.add(video)
            db.session.commit()
            
            # Create bookmark
            bookmark_data = {
                'timestamp': 300,  # 5 minutes
                'label': 'Important Concept',
                'description': 'This explains the key concept'
            }
            
            response = self.client.post(f'/api/videos/{video.id}/bookmarks',
                                      data=json.dumps(bookmark_data),
                                      content_type='application/json')
            self.assertEqual(response.status_code, 201)
            
            # Get bookmarks
            response = self.client.get(f'/api/videos/{video.id}/bookmarks')
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['label'], 'Important Concept')
    
    def test_notes(self):
        """Test note functionality."""
        with self.app.app_context():
            # Create test data
            course = Course(name="Test Course", path="/test")
            db.session.add(course)
            db.session.commit()
            
            module = Module(name="Test Module", path="/test/module", course_id=course.id)
            db.session.add(module)
            db.session.commit()
            
            video = Video(
                name="Test Video",
                filename="test.mp4",
                path="/test/video.mp4",
                module_id=module.id
            )
            db.session.add(video)
            db.session.commit()
            
            # Create note
            note_data = {
                'timestamp': 600,  # 10 minutes
                'content': 'This is a test note about the video content'
            }
            
            response = self.client.post(f'/api/videos/{video.id}/notes',
                                      data=json.dumps(note_data),
                                      content_type='application/json')
            self.assertEqual(response.status_code, 201)
            
            # Get notes
            response = self.client.get(f'/api/videos/{video.id}/notes')
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(len(data), 1)
            self.assertEqual(data[0]['content'], 'This is a test note about the video content')


def run_manual_tests():
    """Run manual tests to verify the application is working."""
    print("🧪 Running Manual Tests for Course Video Player")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("1. Testing server connectivity...")
    try:
        import requests
        response = requests.get('http://127.0.0.1:5001/')
        if response.status_code == 200:
            print("   ✅ Server is running and accessible")
        else:
            print(f"   ❌ Server returned status code: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Cannot connect to server: {e}")
    
    # Test 2: Check API endpoints
    print("\n2. Testing API endpoints...")
    try:
        import requests
        
        # Test courses endpoint
        response = requests.get('http://127.0.0.1:5001/api/courses')
        if response.status_code == 200:
            print("   ✅ /api/courses endpoint working")
        else:
            print(f"   ❌ /api/courses returned: {response.status_code}")
        
        # Test preferences endpoint
        response = requests.get('http://127.0.0.1:5001/api/preferences')
        if response.status_code == 200:
            print("   ✅ /api/preferences endpoint working")
        else:
            print(f"   ❌ /api/preferences returned: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API test failed: {e}")
    
    # Test 3: Check database
    print("\n3. Testing database...")
    try:
        with app.app_context():
            from models import Course
            courses = Course.query.all()
            print(f"   ✅ Database accessible, found {len(courses)} courses")
    except Exception as e:
        print(f"   ❌ Database test failed: {e}")
    
    print("\n" + "=" * 50)
    print("Manual tests completed!")
    print("\n📋 Next Steps:")
    print("1. Open http://127.0.0.1:5001 in your browser")
    print("2. Try adding a course directory with video files")
    print("3. Test video playback, bookmarks, and notes")
    print("4. Toggle between light and dark themes")


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'manual':
        run_manual_tests()
    else:
        print("Running unit tests...")
        unittest.main()
