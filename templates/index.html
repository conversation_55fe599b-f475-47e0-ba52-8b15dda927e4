{% extends "base.html" %}

{% block title %}Dashboard - Course Player{% endblock %}

{% block content %}
<div class="row">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Courses</h5>
                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#scanDirectoryModal">
                    <i class="bi bi-folder-plus"></i> Add
                </button>
            </div>
            <div class="card-body p-0">
                <div id="coursesList" class="list-group list-group-flush">
                    <!-- Courses will be loaded here -->
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number" id="totalCourses">0</div>
                            <div class="stat-label">Courses</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number" id="totalVideos">0</div>
                            <div class="stat-label">Videos</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="col-md-9">
        <!-- Welcome Section -->
        <div id="welcomeSection" class="text-center py-5">
            <i class="bi bi-play-circle display-1 text-primary"></i>
            <h2 class="mt-3">Welcome to Course Player</h2>
            <p class="lead text-muted">Select a course from the sidebar to start watching, or add a new course directory.</p>
            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#scanDirectoryModal">
                <i class="bi bi-folder-plus me-2"></i>Add Course Directory
            </button>
        </div>
        
        <!-- Course Content -->
        <div id="courseContent" class="d-none">
            <!-- Course Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 id="courseName">Course Name</h2>
                    <p class="text-muted mb-0" id="courseDescription">Course description</p>
                </div>
                <div class="course-stats">
                    <span class="badge bg-primary me-2" id="moduleCount">0 modules</span>
                    <span class="badge bg-secondary" id="videoCount">0 videos</span>
                </div>
            </div>
            
            <!-- Video Player Section -->
            <div id="videoPlayerSection" class="d-none">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-body p-0">
                                <video
                                    id="videoPlayer"
                                    class="video-js vjs-default-skin w-100"
                                    controls
                                    preload="auto"
                                    data-setup='{}'>
                                    <p class="vjs-no-js">
                                        To view this video please enable JavaScript, and consider upgrading to a web browser that
                                        <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>.
                                    </p>
                                </video>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-1" id="currentVideoTitle">Video Title</h5>
                                        <small class="text-muted" id="currentVideoInfo">Duration: 0:00</small>
                                    </div>
                                    <div class="btn-group">
                                        <button class="btn btn-outline-primary btn-sm" id="prevVideoBtn" title="Previous Video">
                                            <i class="bi bi-skip-backward-fill"></i>
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="nextVideoBtn" title="Next Video">
                                            <i class="bi bi-skip-forward-fill"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Video Controls -->
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary btn-sm me-2" id="addBookmarkBtn">
                                            <i class="bi bi-bookmark-plus"></i> Add Bookmark
                                        </button>
                                        <button class="btn btn-secondary btn-sm" id="addNoteBtn">
                                            <i class="bi bi-journal-plus"></i> Add Note
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <div class="btn-group">
                                            <button class="btn btn-outline-secondary btn-sm" data-speed="0.5">0.5x</button>
                                            <button class="btn btn-outline-secondary btn-sm" data-speed="0.75">0.75x</button>
                                            <button class="btn btn-secondary btn-sm active" data-speed="1">1x</button>
                                            <button class="btn btn-outline-secondary btn-sm" data-speed="1.25">1.25x</button>
                                            <button class="btn btn-outline-secondary btn-sm" data-speed="1.5">1.5x</button>
                                            <button class="btn btn-outline-secondary btn-sm" data-speed="2">2x</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Video Sidebar -->
                    <div class="col-lg-4">
                        <!-- Bookmarks -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-bookmarks me-2"></i>Bookmarks
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="bookmarksList" class="list-group list-group-flush">
                                    <div class="list-group-item text-center text-muted">
                                        No bookmarks yet
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-journal-text me-2"></i>Notes
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="notesList" class="list-group list-group-flush">
                                    <div class="list-group-item text-center text-muted">
                                        No notes yet
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modules and Videos -->
            <div id="modulesSection">
                <div id="modulesList">
                    <!-- Modules will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Bookmark Modal -->
<div class="modal fade" id="addBookmarkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Bookmark</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bookmarkForm">
                    <div class="mb-3">
                        <label for="bookmarkLabel" class="form-label">Label</label>
                        <input type="text" class="form-control" id="bookmarkLabel" required>
                    </div>
                    <div class="mb-3">
                        <label for="bookmarkDescription" class="form-label">Description (optional)</label>
                        <textarea class="form-control" id="bookmarkDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Timestamp</label>
                        <input type="text" class="form-control" id="bookmarkTimestamp" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveBookmark">Save Bookmark</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Note Modal -->
<div class="modal fade" id="addNoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Note</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="noteForm">
                    <div class="mb-3">
                        <label for="noteContent" class="form-label">Note Content</label>
                        <textarea class="form-control" id="noteContent" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Timestamp (optional)</label>
                        <input type="text" class="form-control" id="noteTimestamp" readonly>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="includeTimestamp" checked>
                            <label class="form-check-label" for="includeTimestamp">
                                Include current timestamp
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveNote">Save Note</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
{% endblock %}
