<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Course Player{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.5.2/video-js.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body data-bs-theme="light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-play-circle-fill me-2"></i>Course Player
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-house-fill me-1"></i>Dashboard
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Search -->
                    <div class="me-3">
                        <div class="input-group">
                            <input type="text" class="form-control" id="globalSearch" placeholder="Search courses, videos...">
                            <button class="btn btn-outline-light" type="button" id="searchBtn">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button class="btn btn-outline-light me-2" id="themeToggle" title="Toggle Theme">
                        <i class="bi bi-sun-fill" id="themeIcon"></i>
                    </button>
                    
                    <!-- Settings -->
                    <button class="btn btn-outline-light" data-bs-toggle="modal" data-bs-target="#settingsModal" title="Settings">
                        <i class="bi bi-gear-fill"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="mb-3">
                            <label for="playbackSpeed" class="form-label">Default Playback Speed</label>
                            <select class="form-select" id="playbackSpeed">
                                <option value="0.5">0.5x</option>
                                <option value="0.75">0.75x</option>
                                <option value="1" selected>1x</option>
                                <option value="1.25">1.25x</option>
                                <option value="1.5">1.5x</option>
                                <option value="2">2x</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="volume" class="form-label">Default Volume</label>
                            <input type="range" class="form-range" id="volume" min="0" max="1" step="0.1" value="0.8">
                            <div class="d-flex justify-content-between">
                                <small>0%</small>
                                <small>100%</small>
                            </div>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoPlayNext" checked>
                            <label class="form-check-label" for="autoPlayNext">
                                Auto-play next video
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveSettings">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Directory Scanner Modal -->
    <div class="modal fade" id="scanDirectoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-folder-plus me-2"></i>Add Course Directory
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Directory Selection Methods -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">Choose how to select your course directory:</h6>

                            <!-- Directory Picker Option -->
                            <div class="card border-primary mb-3" id="directoryPickerCard">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="bi bi-folder2-open text-primary" style="font-size: 2rem;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1">Browse for Directory</h6>
                                            <p class="card-text text-muted mb-2">Click to open a directory picker and select your course folder</p>
                                            <button type="button" class="btn btn-primary btn-sm" id="browseDirectoryBtn">
                                                <i class="bi bi-folder2-open me-1"></i>Browse Directory
                                            </button>
                                        </div>
                                    </div>
                                    <div id="selectedDirectoryInfo" class="mt-3 d-none">
                                        <div class="alert alert-success d-flex align-items-center">
                                            <i class="bi bi-check-circle-fill me-2"></i>
                                            <div>
                                                <strong>Selected:</strong> <span id="selectedDirectoryPath"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Manual Path Option -->
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="me-3">
                                            <i class="bi bi-keyboard text-secondary" style="font-size: 2rem;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1">Enter Path Manually</h6>
                                            <p class="card-text text-muted mb-0">Type the full path to your course directory</p>
                                        </div>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-folder"></i>
                                        </span>
                                        <input type="text" class="form-control" id="directoryPath"
                                               placeholder="e.g., /Users/<USER>/Documents/Courses or C:\Users\<USER>\Documents\Courses">
                                        <div class="valid-feedback">
                                            Path looks valid!
                                        </div>
                                        <div class="invalid-feedback">
                                            Please enter a valid directory path.
                                        </div>
                                    </div>
                                    <div class="form-text mt-2">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Enter the complete path to the directory containing your course videos
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Directory Preview -->
                    <div id="directoryPreview" class="d-none mb-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-eye me-2"></i>Directory Preview
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="directoryContents">
                                    <!-- Directory contents will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Scan Progress -->
                    <div id="scanProgress" class="d-none">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <h6 class="text-primary">Scanning Directory...</h6>
                                <p class="text-muted mb-0">Please wait while we analyze your course directory</p>
                                <div class="progress mt-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                                         role="progressbar" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Scan Results -->
                    <div id="scanResults" class="d-none">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="bi bi-check-circle-fill me-2"></i>Scan Complete!
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <div class="stat-number text-success" id="coursesAdded">0</div>
                                            <div class="stat-label">Courses Added</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <div class="stat-number text-info" id="modulesAdded">0</div>
                                            <div class="stat-label">Modules Added</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <div class="stat-number text-primary" id="videosAdded">0</div>
                                            <div class="stat-label">Videos Added</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <ul id="scanResultsList" class="list-unstyled mb-0"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" id="startScan">
                        <i class="bi bi-search me-1"></i>Start Scan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notificationToast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.5.2/video.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
