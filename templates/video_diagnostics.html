<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player Diagnostics</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.5.2/video-js.css" rel="stylesheet">
    
    <style>
        .diagnostic-section {
            margin-bottom: 2rem;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 0.25rem; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Video Player Diagnostics</h1>
        <p class="text-muted">This page will help diagnose video playback issues.</p>
        
        <!-- System Check -->
        <div class="diagnostic-section">
            <h3>System Check</h3>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Video.js Loaded:</strong> <span id="videojsStatus">Checking...</span></p>
                            <p><strong>Video.js Version:</strong> <span id="videojsVersion">-</span></p>
                            <p><strong>Browser:</strong> <span id="browserInfo">-</span></p>
                            <p><strong>HTML5 Video Support:</strong> <span id="html5Support">Checking...</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>MP4 Support:</strong> <span id="mp4Support">Checking...</span></p>
                            <p><strong>WebM Support:</strong> <span id="webmSupport">Checking...</span></p>
                            <p><strong>Console Errors:</strong> <span id="consoleErrors">0</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- API Tests -->
        <div class="diagnostic-section">
            <h3>API Tests</h3>
            <div class="card">
                <div class="card-body">
                    <button id="testApiBtn" class="btn btn-primary">Run API Tests</button>
                    <div id="apiResults" class="mt-3"></div>
                </div>
            </div>
        </div>
        
        <!-- Video Player Tests -->
        <div class="diagnostic-section">
            <h3>Video Player Tests</h3>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- HTML5 Video Test -->
                            <h5>HTML5 Video Test</h5>
                            <video id="html5Video" width="400" height="225" controls style="max-width: 100%;">
                                <source src="/api/videos/1/stream" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            <div class="mt-2">
                                <button id="loadHtml5Video" class="btn btn-sm btn-primary">Load Video</button>
                                <span id="html5Status" class="ms-2">Ready</span>
                            </div>
                            
                            <!-- Video.js Test -->
                            <h5 class="mt-4">Video.js Test</h5>
                            <video
                                id="videojsPlayer"
                                class="video-js vjs-default-skin"
                                controls
                                preload="auto"
                                width="400"
                                height="225"
                                data-setup='{}'>
                                <p class="vjs-no-js">
                                    To view this video please enable JavaScript.
                                </p>
                            </video>
                            <div class="mt-2">
                                <button id="loadVideojsVideo" class="btn btn-sm btn-primary">Load Video</button>
                                <button id="initVideojs" class="btn btn-sm btn-secondary">Initialize Video.js</button>
                                <span id="videojsStatus2" class="ms-2">Ready</span>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <h5>Video Information</h5>
                            <div id="videoInfo">
                                <button id="loadVideoInfo" class="btn btn-sm btn-outline-primary">Load Info</button>
                            </div>
                            
                            <h5 class="mt-3">Event Log</h5>
                            <div id="eventLog" style="height: 200px; overflow-y: auto; border: 1px solid #dee2e6; padding: 0.5rem; font-size: 0.8rem;">
                                <div class="text-muted">Events will appear here...</div>
                            </div>
                            <button id="clearLog" class="btn btn-sm btn-outline-secondary mt-2">Clear Log</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Network Tests -->
        <div class="diagnostic-section">
            <h3>Network Tests</h3>
            <div class="card">
                <div class="card-body">
                    <button id="testNetworkBtn" class="btn btn-primary">Run Network Tests</button>
                    <div id="networkResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.5.2/video.min.js"></script>
    
    <script>
        let player = null;
        let errorCount = 0;
        
        // Capture console errors
        const originalError = console.error;
        console.error = function(...args) {
            errorCount++;
            document.getElementById('consoleErrors').textContent = errorCount;
            logEvent('Console Error: ' + args.join(' '), 'error');
            originalError.apply(console, args);
        };
        
        function logEvent(message, type = 'info') {
            const log = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'text-danger' : type === 'warning' ? 'text-warning' : 'text-info';
            log.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function setStatus(elementId, status, isGood = null) {
            const element = document.getElementById(elementId);
            element.textContent = status;
            if (isGood === true) element.className = 'status-good';
            else if (isGood === false) element.className = 'status-bad';
            else element.className = 'status-warning';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // System checks
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
            
            // Check Video.js
            if (typeof videojs !== 'undefined') {
                setStatus('videojsStatus', 'Loaded', true);
                setStatus('videojsVersion', videojs.VERSION || 'Unknown');
                logEvent('Video.js loaded successfully');
            } else {
                setStatus('videojsStatus', 'Not Loaded', false);
                logEvent('Video.js not loaded!', 'error');
            }
            
            // Check HTML5 video support
            const video = document.createElement('video');
            if (video.canPlayType) {
                setStatus('html5Support', 'Supported', true);
                setStatus('mp4Support', video.canPlayType('video/mp4') ? 'Supported' : 'Not Supported', 
                         video.canPlayType('video/mp4') ? true : false);
                setStatus('webmSupport', video.canPlayType('video/webm') ? 'Supported' : 'Not Supported',
                         video.canPlayType('video/webm') ? true : false);
            } else {
                setStatus('html5Support', 'Not Supported', false);
            }
            
            // Set up event listeners
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // HTML5 video events
            const html5Video = document.getElementById('html5Video');
            html5Video.addEventListener('loadstart', () => logEvent('HTML5: Load started'));
            html5Video.addEventListener('canplay', () => logEvent('HTML5: Can play'));
            html5Video.addEventListener('error', (e) => logEvent('HTML5: Error - ' + e.message, 'error'));
            html5Video.addEventListener('play', () => logEvent('HTML5: Playing'));
            html5Video.addEventListener('pause', () => logEvent('HTML5: Paused'));
            
            // Button events
            document.getElementById('loadHtml5Video').addEventListener('click', () => {
                html5Video.load();
                logEvent('HTML5: Loading video...');
            });
            
            document.getElementById('initVideojs').addEventListener('click', initializeVideojs);
            document.getElementById('loadVideojsVideo').addEventListener('click', loadVideojsVideo);
            document.getElementById('loadVideoInfo').addEventListener('click', loadVideoInfo);
            document.getElementById('testApiBtn').addEventListener('click', testApi);
            document.getElementById('testNetworkBtn').addEventListener('click', testNetwork);
            document.getElementById('clearLog').addEventListener('click', () => {
                document.getElementById('eventLog').innerHTML = '<div class="text-muted">Events will appear here...</div>';
            });
        }
        
        function initializeVideojs() {
            try {
                if (player) {
                    player.dispose();
                    logEvent('Video.js: Disposed existing player');
                }
                
                player = videojs('videojsPlayer', {
                    controls: true,
                    responsive: true,
                    fluid: false,
                    width: 400,
                    height: 225
                });
                
                player.on('loadstart', () => logEvent('Video.js: Load started'));
                player.on('canplay', () => logEvent('Video.js: Can play'));
                player.on('error', (e) => {
                    const error = player.error();
                    logEvent(`Video.js: Error ${error?.code} - ${error?.message}`, 'error');
                });
                player.on('play', () => logEvent('Video.js: Playing'));
                player.on('pause', () => logEvent('Video.js: Paused'));
                
                logEvent('Video.js: Player initialized');
                setStatus('videojsStatus2', 'Initialized', true);
                
            } catch (error) {
                logEvent('Video.js: Initialization error - ' + error.message, 'error');
                setStatus('videojsStatus2', 'Error', false);
            }
        }
        
        function loadVideojsVideo() {
            if (!player) {
                logEvent('Video.js: Player not initialized', 'warning');
                return;
            }
            
            try {
                player.src({
                    src: '/api/videos/1/stream',
                    type: 'video/mp4'
                });
                logEvent('Video.js: Source set');
            } catch (error) {
                logEvent('Video.js: Error setting source - ' + error.message, 'error');
            }
        }
        
        async function loadVideoInfo() {
            try {
                const response = await fetch('/debug-video/1');
                const data = await response.json();
                
                let html = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                document.getElementById('videoInfo').innerHTML = html;
                logEvent('Video info loaded');
            } catch (error) {
                logEvent('Error loading video info: ' + error.message, 'error');
            }
        }
        
        async function testApi() {
            const results = document.getElementById('apiResults');
            results.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            const tests = [
                { name: 'Get Courses', url: '/api/courses' },
                { name: 'Get Video Info', url: '/api/videos/1' },
                { name: 'Video Stream (HEAD)', url: '/api/videos/1/stream', method: 'HEAD' },
                { name: 'Debug Video', url: '/debug-video/1' }
            ];
            
            let html = '<div class="row">';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { method: test.method || 'GET' });
                    const status = response.ok ? 'success' : 'danger';
                    html += `<div class="col-md-6 mb-2">
                        <span class="badge bg-${status}">${response.status}</span> ${test.name}
                    </div>`;
                    logEvent(`API Test: ${test.name} - ${response.status}`);
                } catch (error) {
                    html += `<div class="col-md-6 mb-2">
                        <span class="badge bg-danger">ERROR</span> ${test.name}
                    </div>`;
                    logEvent(`API Test: ${test.name} - Error: ${error.message}`, 'error');
                }
            }
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        async function testNetwork() {
            const results = document.getElementById('networkResults');
            results.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            try {
                // Test range request
                const response = await fetch('/api/videos/1/stream', {
                    headers: { 'Range': 'bytes=0-1023' }
                });
                
                const contentRange = response.headers.get('Content-Range');
                const acceptRanges = response.headers.get('Accept-Ranges');
                
                let html = `
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Range Request:</strong> <span class="badge bg-${response.status === 206 ? 'success' : 'danger'}">${response.status}</span></p>
                            <p><strong>Accept-Ranges:</strong> ${acceptRanges || 'Not set'}</p>
                            <p><strong>Content-Range:</strong> ${contentRange || 'Not set'}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Content-Type:</strong> ${response.headers.get('Content-Type')}</p>
                            <p><strong>Content-Length:</strong> ${response.headers.get('Content-Length')}</p>
                        </div>
                    </div>
                `;
                
                results.innerHTML = html;
                logEvent(`Network Test: Range request - ${response.status}`);
                
            } catch (error) {
                results.innerHTML = `<div class="alert alert-danger">Network test failed: ${error.message}</div>`;
                logEvent(`Network Test: Error - ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
