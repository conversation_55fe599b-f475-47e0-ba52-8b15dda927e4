<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.5.2/video-js.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Video Player Test</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body p-0">
                        <video
                            id="testVideoPlayer"
                            class="video-js vjs-default-skin w-100"
                            controls
                            preload="auto"
                            width="800"
                            height="450"
                            data-setup='{}'>
                            <p class="vjs-no-js">
                                To view this video please enable JavaScript, and consider upgrading to a web browser that
                                <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>.
                            </p>
                        </video>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h5>Test Controls</h5>
                    <button id="loadVideo1" class="btn btn-primary">Load Video 1</button>
                    <button id="loadVideo2" class="btn btn-secondary">Load Video 2</button>
                    <button id="loadVideo3" class="btn btn-success">Load Video 3</button>
                </div>
                
                <div class="mt-3">
                    <h5>Debug Info</h5>
                    <div id="debugInfo" class="alert alert-info">
                        <p><strong>Video.js Version:</strong> <span id="videojsVersion">Loading...</span></p>
                        <p><strong>Player State:</strong> <span id="playerState">Not initialized</span></p>
                        <p><strong>Current Source:</strong> <span id="currentSource">None</span></p>
                        <p><strong>Errors:</strong> <span id="errorInfo">None</span></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Available Videos</h5>
                    </div>
                    <div class="card-body">
                        <div id="videoList">Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.5.2/video.min.js"></script>
    
    <script>
        let player = null;
        let videos = [];
        
        document.addEventListener('DOMContentLoaded', async function() {
            // Check if Video.js is loaded
            if (typeof videojs !== 'undefined') {
                document.getElementById('videojsVersion').textContent = videojs.VERSION || 'Unknown';
                
                // Initialize player
                player = videojs('testVideoPlayer', {
                    controls: true,
                    responsive: true,
                    fluid: true,
                    playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2]
                });
                
                document.getElementById('playerState').textContent = 'Initialized';
                
                // Set up event listeners
                player.on('loadstart', () => {
                    console.log('Video loading started');
                    document.getElementById('playerState').textContent = 'Loading...';
                });
                
                player.on('canplay', () => {
                    console.log('Video can start playing');
                    document.getElementById('playerState').textContent = 'Ready to play';
                });
                
                player.on('error', (error) => {
                    console.error('Video player error:', error);
                    const errorCode = player.error()?.code || 0;
                    document.getElementById('errorInfo').textContent = `Error ${errorCode}: ${player.error()?.message || 'Unknown error'}`;
                    document.getElementById('playerState').textContent = 'Error';
                });
                
                player.on('play', () => {
                    document.getElementById('playerState').textContent = 'Playing';
                });
                
                player.on('pause', () => {
                    document.getElementById('playerState').textContent = 'Paused';
                });
                
            } else {
                document.getElementById('videojsVersion').textContent = 'Not loaded!';
                document.getElementById('playerState').textContent = 'Video.js not available';
            }
            
            // Load available videos
            try {
                const response = await fetch('/api/courses');
                const courses = await response.json();
                
                let videoListHtml = '';
                courses.forEach(course => {
                    videoListHtml += `<h6>${course.name}</h6>`;
                    // We'll need to fetch course details to get videos
                });
                
                // Fetch first course details if available
                if (courses.length > 0) {
                    const courseResponse = await fetch(`/api/courses/${courses[0].id}`);
                    const courseData = await courseResponse.json();
                    
                    courseData.modules.forEach(module => {
                        module.videos.forEach(video => {
                            videos.push(video);
                            videoListHtml += `<div class="mb-2">
                                <button class="btn btn-sm btn-outline-primary w-100" onclick="loadVideo(${video.id})">
                                    ${video.name}
                                </button>
                            </div>`;
                        });
                    });
                }
                
                document.getElementById('videoList').innerHTML = videoListHtml || 'No videos found';
                
            } catch (error) {
                console.error('Error loading videos:', error);
                document.getElementById('videoList').innerHTML = 'Error loading videos';
            }
            
            // Set up test buttons
            document.getElementById('loadVideo1').addEventListener('click', () => {
                if (videos.length > 0) loadVideo(videos[0].id);
            });
            
            document.getElementById('loadVideo2').addEventListener('click', () => {
                if (videos.length > 1) loadVideo(videos[1].id);
            });
            
            document.getElementById('loadVideo3').addEventListener('click', () => {
                if (videos.length > 2) loadVideo(videos[2].id);
            });
        });
        
        function loadVideo(videoId) {
            if (!player) {
                console.error('Player not initialized');
                return;
            }
            
            const video = videos.find(v => v.id === videoId);
            if (!video) {
                console.error('Video not found');
                return;
            }
            
            console.log('Loading video:', video);
            
            const videoType = getVideoMimeType(video.filename);
            const streamUrl = `/api/videos/${videoId}/stream`;
            
            document.getElementById('currentSource').textContent = streamUrl;
            document.getElementById('errorInfo').textContent = 'None';
            
            player.src({
                src: streamUrl,
                type: videoType
            });
            
            console.log('Video source set:', streamUrl, videoType);
        }
        
        function getVideoMimeType(filename) {
            const extension = filename.split('.').pop().toLowerCase();
            const mimeTypes = {
                'mp4': 'video/mp4',
                'avi': 'video/x-msvideo',
                'mkv': 'video/x-matroska',
                'mov': 'video/quicktime',
                'wmv': 'video/x-ms-wmv',
                'flv': 'video/x-flv',
                'webm': 'video/webm',
                'm4v': 'video/mp4'
            };
            return mimeTypes[extension] || 'video/mp4';
        }
    </script>
</body>
</html>
