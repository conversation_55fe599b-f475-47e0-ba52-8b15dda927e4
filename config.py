import os
from pathlib import Path

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///course_player.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Video settings
    SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v']
    THUMBNAIL_SIZE = (320, 180)
    
    # Upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024 * 1024  # 16GB max file size
    
    # Course directory settings
    DEFAULT_COURSE_PATH = os.path.join(Path.home(), 'Videos', 'Courses')
    
    # UI settings
    VIDEOS_PER_PAGE = 20
    
class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
