# Course Video Player

A powerful, feature-rich Flask-based video player designed specifically for course content with advanced learning features.

## 🚀 Features

### Core Functionality
- **Directory Scanning**: Automatically scan and organize course directories
- **Course Organization**: Auto-group videos by folder structure (Course > Module > Videos)
- **Video Metadata**: Extract duration, resolution, file size, and generate thumbnails
- **Resume Playback**: Save and resume video progress automatically
- **Multi-format Support**: Supports MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V

### Advanced Learning Features
- **Bookmarks**: Save timestamped bookmarks with custom labels and descriptions
- **Notes**: Add personal notes with optional timestamps
- **Tags**: Organize videos with custom tags and colors
- **Progress Tracking**: Visual progress bars for each video and module
- **Search**: Global search across courses, modules, and videos

### User Experience
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Dark/Light Theme**: Toggle between themes with user preference saving
- **Keyboard Shortcuts**: Space (play/pause), arrows (seek/volume), etc.
- **Speed Control**: Playback speed from 0.5x to 2x
- **Auto-play**: Optional auto-play next video in sequence

### Technical Features
- **SQLite Database**: Local storage for all data
- **Video.js Player**: Professional HTML5 video player
- **Bootstrap UI**: Modern, responsive interface
- **RESTful API**: Clean API for all operations
- **Session Management**: Multi-user support with session tracking

## 📋 Requirements

- Python 3.8+
- Flask 2.3.3
- SQLite (included with Python)
- FFmpeg (for thumbnail generation)

## 🛠️ Installation

1. **Clone or download the project**
   ```bash
   cd course_player
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open in browser**
   ```
   http://localhost:5001
   ```

## 📁 Project Structure

```
course_player/
├── app.py                 # Main Flask application
├── models.py             # Database models
├── routes.py             # API routes
├── utils.py              # Utility functions
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── templates/
│   ├── base.html        # Base template
│   └── index.html       # Main dashboard
├── static/
│   ├── css/
│   │   └── style.css    # Custom styles
│   ├── js/
│   │   ├── app.js       # Main application logic
│   │   └── dashboard.js # Dashboard functionality
│   ├── images/          # Static images
│   └── thumbnails/      # Generated video thumbnails
└── course_player.db     # SQLite database (created automatically)
```

## 🎯 Usage

### Adding Courses

1. Click the "Add Course Directory" button
2. Enter the full path to your course directory
3. Click "Start Scan" to automatically organize your videos

### Directory Structure Examples

The application supports various directory structures:

**Single Course:**
```
/path/to/course/
├── 01_Introduction.mp4
├── 02_Getting_Started.mp4
└── 03_Advanced_Topics.mp4
```

**Course with Modules:**
```
/path/to/course/
├── Module_1_Basics/
│   ├── 01_Introduction.mp4
│   └── 02_Setup.mp4
└── Module_2_Advanced/
    ├── 01_Advanced_Concepts.mp4
    └── 02_Best_Practices.mp4
```

**Multiple Courses:**
```
/path/to/courses/
├── Python_Course/
│   ├── Basics/
│   └── Advanced/
└── JavaScript_Course/
    ├── Fundamentals/
    └── Frameworks/
```

### Video Player Controls

- **Space**: Play/Pause
- **Left/Right Arrows**: Seek backward/forward (10 seconds)
- **Up/Down Arrows**: Volume up/down
- **Speed Buttons**: Change playback speed (0.5x to 2x)
- **Previous/Next**: Navigate between videos in sequence

### Bookmarks and Notes

- **Add Bookmark**: Click the bookmark button during video playback
- **Add Note**: Click the note button to add timestamped or general notes
- **Quick Navigation**: Click on bookmarks/notes to jump to that timestamp

## 🔧 Configuration

Edit `config.py` to customize:

- **Supported video formats**
- **Thumbnail size**
- **Default course directory**
- **Videos per page**
- **Maximum file size**

## 🎨 Customization

### Themes
The application supports light and dark themes. Users can toggle between themes using the theme button in the navigation bar.

### UI Customization
Modify `static/css/style.css` to customize the appearance:
- Colors and themes
- Layout and spacing
- Component styling
- Responsive breakpoints

## 📊 Database Schema

The application uses SQLite with the following main tables:

- **courses**: Course information and metadata
- **modules**: Course modules/chapters
- **videos**: Video files and metadata
- **video_progress**: User progress tracking
- **bookmarks**: Timestamped bookmarks
- **notes**: User notes with optional timestamps
- **tags**: Custom tags for organization
- **user_preferences**: User settings and preferences

## 🚀 API Endpoints

### Courses
- `GET /api/courses` - List all courses
- `GET /api/courses/{id}` - Get course with modules and videos

### Videos
- `GET /api/videos/{id}` - Get video details with progress, bookmarks, notes
- `GET /api/videos/{id}/stream` - Stream video file
- `POST /api/videos/{id}/progress` - Update video progress

### Bookmarks & Notes
- `GET/POST /api/videos/{id}/bookmarks` - Get/create bookmarks
- `DELETE /api/bookmarks/{id}` - Delete bookmark
- `GET/POST /api/videos/{id}/notes` - Get/create notes
- `PUT/DELETE /api/notes/{id}` - Update/delete note

### Utility
- `GET /api/search?q={query}` - Search courses, modules, videos
- `POST /api/scan-directory` - Scan directory for courses
- `GET/POST /api/preferences` - Get/update user preferences

## 🔍 Troubleshooting

### Common Issues

1. **Port already in use**
   - Change the port in `app.py` (line 32)
   - Or kill the process using port 5001

2. **Video not playing**
   - Ensure the video file exists and is accessible
   - Check if the video format is supported
   - Verify file permissions

3. **Thumbnails not generating**
   - Install FFmpeg: `brew install ffmpeg` (macOS) or equivalent
   - Ensure moviepy is properly installed

4. **Directory scan fails**
   - Check directory permissions
   - Ensure the path exists and contains video files
   - Check the application logs for specific errors

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Flask** - Web framework
- **Video.js** - HTML5 video player
- **Bootstrap** - UI framework
- **MoviePy** - Video processing
- **SQLAlchemy** - Database ORM
