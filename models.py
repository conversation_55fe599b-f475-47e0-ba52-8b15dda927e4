from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()
from datetime import datetime
import json

class Course(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    path = db.Column(db.String(500), nullable=False, unique=True)
    thumbnail_path = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    total_duration = db.Column(db.Integer, default=0)  # in seconds
    
    # Relationships
    modules = db.relationship('Module', backref='course', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Course {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'path': self.path,
            'thumbnail_path': self.thumbnail_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'total_duration': self.total_duration,
            'modules_count': len(self.modules)
        }

class Module(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    path = db.Column(db.String(500), nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('course.id'), nullable=False)
    order_index = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    videos = db.relationship('Video', backref='module', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Module {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'path': self.path,
            'course_id': self.course_id,
            'order_index': self.order_index,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'videos_count': len(self.videos)
        }

class Video(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    filename = db.Column(db.String(200), nullable=False)
    path = db.Column(db.String(500), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('module.id'), nullable=False)
    order_index = db.Column(db.Integer, default=0)
    duration = db.Column(db.Integer, default=0)  # in seconds
    file_size = db.Column(db.BigInteger, default=0)  # in bytes
    resolution = db.Column(db.String(20))  # e.g., "1920x1080"
    thumbnail_path = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    progress_records = db.relationship('VideoProgress', backref='video', lazy=True, cascade='all, delete-orphan')
    bookmarks = db.relationship('Bookmark', backref='video', lazy=True, cascade='all, delete-orphan')
    notes = db.relationship('Note', backref='video', lazy=True, cascade='all, delete-orphan')
    tags = db.relationship('VideoTag', backref='video', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Video {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'filename': self.filename,
            'path': self.path,
            'module_id': self.module_id,
            'order_index': self.order_index,
            'duration': self.duration,
            'file_size': self.file_size,
            'resolution': self.resolution,
            'thumbnail_path': self.thumbnail_path,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class VideoProgress(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('video.id'), nullable=False)
    user_session = db.Column(db.String(100), nullable=False)  # For multi-user support later
    current_time = db.Column(db.Integer, default=0)  # in seconds
    completed = db.Column(db.Boolean, default=False)
    last_watched = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('video_id', 'user_session'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'video_id': self.video_id,
            'user_session': self.user_session,
            'current_time': self.current_time,
            'completed': self.completed,
            'last_watched': self.last_watched.isoformat() if self.last_watched else None
        }

class Bookmark(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('video.id'), nullable=False)
    user_session = db.Column(db.String(100), nullable=False)
    timestamp = db.Column(db.Integer, nullable=False)  # in seconds
    label = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'video_id': self.video_id,
            'user_session': self.user_session,
            'timestamp': self.timestamp,
            'label': self.label,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Note(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('video.id'), nullable=False)
    user_session = db.Column(db.String(100), nullable=False)
    timestamp = db.Column(db.Integer)  # in seconds, optional
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'video_id': self.video_id,
            'user_session': self.user_session,
            'timestamp': self.timestamp,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Tag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    color = db.Column(db.String(7), default='#007bff')  # hex color
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'color': self.color,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class VideoTag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.Integer, db.ForeignKey('video.id'), nullable=False)
    tag_id = db.Column(db.Integer, db.ForeignKey('tag.id'), nullable=False)
    user_session = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('video_id', 'tag_id', 'user_session'),)

class UserPreference(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_session = db.Column(db.String(100), nullable=False, unique=True)
    preferences = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_preferences(self):
        if self.preferences:
            return json.loads(self.preferences)
        return {
            'theme': 'light',
            'playback_speed': 1.0,
            'auto_play_next': True,
            'volume': 0.8,
            'quality': 'auto'
        }
    
    def set_preferences(self, prefs):
        self.preferences = json.dumps(prefs)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_session': self.user_session,
            'preferences': self.get_preferences(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
