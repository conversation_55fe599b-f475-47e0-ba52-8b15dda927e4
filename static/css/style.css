/* Custom CSS for Course Player */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Dark theme variables */
[data-bs-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
    --bs-card-bg: #2d2d2d;
    --bs-border-color: #404040;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.3s ease;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Course List */
.course-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 4px solid transparent;
}

.course-item:hover {
    background-color: var(--bs-light);
    border-left-color: var(--primary-color);
}

.course-item.active {
    background-color: var(--primary-color);
    color: white;
    border-left-color: var(--primary-color);
}

[data-bs-theme="dark"] .course-item:hover {
    background-color: var(--bs-dark);
}

/* Video Player */
.video-js {
    border-radius: 0.375rem;
}

.video-js .vjs-big-play-button {
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.8);
    border: none;
    font-size: 2.5em;
}

.video-js .vjs-control-bar {
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.7));
}

/* Module Cards */
.module-card {
    margin-bottom: 1.5rem;
}

.module-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 0.375rem 0.375rem 0 0;
}

.video-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.video-item:hover {
    background-color: var(--bs-light);
    border-left-color: var(--primary-color);
}

.video-item.active {
    background-color: rgba(13, 110, 253, 0.1);
    border-left-color: var(--primary-color);
}

[data-bs-theme="dark"] .video-item:hover {
    background-color: var(--bs-dark);
}

.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 0.25rem;
}

.video-duration {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.7rem;
}

/* Progress Bars */
.progress-bar-video {
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.progress-container {
    height: 4px;
    background-color: var(--bs-border-color);
    border-radius: 2px;
    overflow: hidden;
}

/* Stats */
.stat-item {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--bs-secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Bookmarks and Notes */
.bookmark-item, .note-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.bookmark-item:hover, .note-item:hover {
    background-color: var(--bs-light);
}

[data-bs-theme="dark"] .bookmark-item:hover,
[data-bs-theme="dark"] .note-item:hover {
    background-color: var(--bs-dark);
}

.timestamp-badge {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
}

/* Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

[data-bs-theme="dark"] .search-results {
    background: var(--bs-dark);
    border-color: var(--bs-border-color);
}

.search-result-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--bs-border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: var(--bs-light);
}

[data-bs-theme="dark"] .search-result-item:hover {
    background-color: var(--bs-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Speed Control Buttons */
.btn-group .btn[data-speed] {
    min-width: 50px;
}

.btn-group .btn[data-speed].active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .col-md-3 {
        margin-bottom: 2rem;
    }
    
    .video-thumbnail {
        width: 60px;
        height: 34px;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bs-light);
}

::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bs-dark);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
}

/* Toast Notifications */
.toast {
    min-width: 300px;
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

/* Welcome Section */
#welcomeSection {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Course Header */
.course-stats .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* Video Controls */
.video-controls {
    background: var(--bs-light);
    border-radius: 0.375rem;
    padding: 1rem;
}

[data-bs-theme="dark"] .video-controls {
    background: var(--bs-dark);
}

/* Directory Scanner Modal Enhancements */
#scanDirectoryModal .modal-header {
    border-bottom: none;
}

#scanDirectoryModal .card {
    transition: all 0.3s ease;
    cursor: pointer;
}

#scanDirectoryModal .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#directoryPickerCard {
    border-width: 2px;
}

#directoryPickerCard:hover {
    border-color: var(--primary-color) !important;
    background-color: rgba(13, 110, 253, 0.05);
}

.stat-item {
    padding: 1rem 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--bs-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Directory Preview Styles */
#directoryPreview .card {
    border: 1px solid var(--bs-border-color);
}

#directoryContents {
    max-height: 200px;
    overflow-y: auto;
}

.directory-item {
    padding: 0.5rem;
    border-bottom: 1px solid var(--bs-border-color);
    display: flex;
    align-items: center;
}

.directory-item:last-child {
    border-bottom: none;
}

.directory-item i {
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

.directory-item.folder {
    color: var(--warning-color);
}

.directory-item.video {
    color: var(--primary-color);
}

/* Enhanced Progress Indicators */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Scan Results Enhancements */
#scanResults .stat-item {
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

#scanResults .stat-item:last-child {
    border-right: none;
}

/* Input Group Enhancements */
.input-group-text {
    background-color: var(--bs-light);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .input-group-text {
    background-color: var(--bs-dark);
    color: var(--bs-light);
}

/* Button Enhancements */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.25);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

/* Form Validation Styles */
.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.input-group .form-control.is-valid ~ .valid-feedback,
.input-group .form-control.is-invalid ~ .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Enhanced Card Interactions */
#scanDirectoryModal .card {
    border-radius: 0.75rem;
    overflow: hidden;
}

#scanDirectoryModal .card-body {
    position: relative;
}

#scanDirectoryModal .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

#scanDirectoryModal .card:hover::before {
    opacity: 1;
}

/* Improved Typography */
#scanDirectoryModal h6 {
    font-weight: 600;
    color: var(--bs-dark);
}

[data-bs-theme="dark"] #scanDirectoryModal h6 {
    color: var(--bs-light);
}

#scanDirectoryModal .text-muted {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Enhanced Buttons */
#browseDirectoryBtn {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

#browseDirectoryBtn:hover {
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(13, 110, 253, 0.3);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    #scanDirectoryModal .modal-dialog {
        margin: 0.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    #scanDirectoryModal .row .col-md-4 {
        margin-bottom: 1rem;
    }

    #scanDirectoryModal .card-body {
        padding: 1rem;
    }

    #scanDirectoryModal h6 {
        font-size: 1rem;
    }

    #browseDirectoryBtn {
        width: 100%;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    #scanDirectoryModal .modal-dialog {
        margin: 0.25rem;
    }

    #scanDirectoryModal .card {
        margin-bottom: 1rem;
    }

    .directory-item {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }
}
