// Dashboard-specific JavaScript

class Dashboard {
    constructor() {
        this.currentCourse = null;
        this.currentVideo = null;
        this.player = null;
        this.videos = [];
        this.currentVideoIndex = -1;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Video player controls
        document.getElementById('prevVideoBtn').addEventListener('click', () => {
            this.playPreviousVideo();
        });
        
        document.getElementById('nextVideoBtn').addEventListener('click', () => {
            this.playNextVideo();
        });
        
        // Speed control buttons
        document.querySelectorAll('[data-speed]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setPlaybackSpeed(parseFloat(e.target.dataset.speed));
                
                // Update active button
                document.querySelectorAll('[data-speed]').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Bookmark and note buttons
        document.getElementById('addBookmarkBtn').addEventListener('click', () => {
            this.showAddBookmarkModal();
        });
        
        document.getElementById('addNoteBtn').addEventListener('click', () => {
            this.showAddNoteModal();
        });
        
        // Save bookmark
        document.getElementById('saveBookmark').addEventListener('click', () => {
            this.saveBookmark();
        });
        
        // Save note
        document.getElementById('saveNote').addEventListener('click', () => {
            this.saveNote();
        });
        
        // Directory scanner
        document.getElementById('startScan').addEventListener('click', () => {
            this.scanDirectory();
        });
    }
    
    async loadCourse(courseId) {
        try {
            const response = await fetch(`/api/courses/${courseId}`);
            const course = await response.json();
            
            this.currentCourse = course;
            this.renderCourse(course);
            
            // Hide welcome section and show course content
            document.getElementById('welcomeSection').classList.add('d-none');
            document.getElementById('courseContent').classList.remove('d-none');
            
            // Update active course in sidebar
            document.querySelectorAll('.course-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Build videos array for navigation
            this.buildVideosArray(course);
            
        } catch (error) {
            console.error('Error loading course:', error);
            window.coursePlayer.showToast('Error loading course', 'error');
        }
    }
    
    buildVideosArray(course) {
        this.videos = [];
        course.modules.forEach(module => {
            module.videos.forEach(video => {
                this.videos.push({
                    ...video,
                    module_name: module.name
                });
            });
        });
    }
    
    renderCourse(course) {
        // Update course header
        document.getElementById('courseName').textContent = course.name;
        document.getElementById('courseDescription').textContent = course.description || 'No description available';
        document.getElementById('moduleCount').textContent = `${course.modules.length} modules`;
        
        let totalVideos = 0;
        course.modules.forEach(module => {
            totalVideos += module.videos.length;
        });
        document.getElementById('videoCount').textContent = `${totalVideos} videos`;
        
        // Render modules
        this.renderModules(course.modules);
    }
    
    renderModules(modules) {
        const modulesList = document.getElementById('modulesList');
        modulesList.innerHTML = '';
        
        modules.forEach((module, moduleIndex) => {
            const moduleCard = document.createElement('div');
            moduleCard.className = 'card module-card';
            
            moduleCard.innerHTML = `
                <div class="card-header module-header">
                    <h5 class="mb-0">
                        <i class="bi bi-folder-fill me-2"></i>${module.name}
                        <span class="badge bg-light text-dark ms-2">${module.videos.length} videos</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="module-${module.id}-videos">
                        <!-- Videos will be rendered here -->
                    </div>
                </div>
            `;
            
            modulesList.appendChild(moduleCard);
            
            // Render videos for this module
            this.renderModuleVideos(module);
        });
    }
    
    renderModuleVideos(module) {
        const videosList = document.getElementById(`module-${module.id}-videos`);
        
        module.videos.forEach((video, videoIndex) => {
            const videoItem = document.createElement('div');
            videoItem.className = 'list-group-item video-item';
            videoItem.dataset.videoId = video.id;
            
            const thumbnailSrc = video.thumbnail_path ? 
                `/static/thumbnails/${video.thumbnail_path}` : 
                '/static/images/video-placeholder.jpg';
            
            videoItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="position-relative me-3">
                        <img src="${thumbnailSrc}" alt="${video.name}" class="video-thumbnail">
                        <div class="video-duration">${window.coursePlayer.formatDuration(video.duration)}</div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${video.name}</h6>
                        <small class="text-muted">
                            ${window.coursePlayer.formatFileSize(video.file_size)} • ${video.resolution || 'Unknown resolution'}
                        </small>
                        <div class="progress-container mt-1">
                            <div class="progress-bar-video" style="width: 0%" data-video-id="${video.id}"></div>
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle-fill text-primary fs-4"></i>
                    </div>
                </div>
            `;
            
            videoItem.addEventListener('click', () => {
                this.playVideo(video);
            });
            
            videosList.appendChild(videoItem);
        });
        
        // Load progress for videos
        this.loadVideoProgress(module.videos);
    }
    
    async loadVideoProgress(videos) {
        // Load progress for each video and update progress bars
        videos.forEach(async (video) => {
            try {
                const response = await fetch(`/api/videos/${video.id}`);
                const videoData = await response.json();
                
                if (videoData.progress && videoData.progress.current_time > 0) {
                    const progressPercent = (videoData.progress.current_time / video.duration) * 100;
                    const progressBar = document.querySelector(`[data-video-id="${video.id}"]`);
                    if (progressBar) {
                        progressBar.style.width = `${progressPercent}%`;
                    }
                }
            } catch (error) {
                console.error(`Error loading progress for video ${video.id}:`, error);
            }
        });
    }
    
    async playVideo(video) {
        try {
            this.currentVideo = video;
            this.currentVideoIndex = this.videos.findIndex(v => v.id === video.id);
            
            // Show video player section
            document.getElementById('videoPlayerSection').classList.remove('d-none');
            document.getElementById('modulesSection').style.display = 'block';
            
            // Update video info
            document.getElementById('currentVideoTitle').textContent = video.name;
            document.getElementById('currentVideoInfo').textContent = 
                `Duration: ${window.coursePlayer.formatDuration(video.duration)} • ${window.coursePlayer.formatFileSize(video.file_size)}`;
            
            // Initialize or update video player
            this.initializeVideoPlayer(video);
            
            // Update active video in list
            document.querySelectorAll('.video-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-video-id="${video.id}"]`).classList.add('active');
            
            // Load video data (bookmarks, notes, etc.)
            await this.loadVideoData(video.id);
            
        } catch (error) {
            console.error('Error playing video:', error);
            window.coursePlayer.showToast('Error playing video', 'error');
        }
    }
    
    initializeVideoPlayer(video) {
        // Dispose existing player if it exists
        if (this.player) {
            this.player.dispose();
        }

        // Determine video MIME type based on file extension
        const videoType = this.getVideoMimeType(video.filename);

        // Initialize Video.js player
        this.player = videojs('videoPlayer', {
            controls: true,
            responsive: true,
            fluid: true,
            playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
            sources: [{
                src: `/api/videos/${video.id}/stream`,
                type: videoType
            }],
            html5: {
                vhs: {
                    overrideNative: true
                }
            }
        });

        // Apply user preferences
        this.player.playbackRate(window.coursePlayer.preferences.playback_speed || 1);
        this.player.volume(window.coursePlayer.preferences.volume || 0.8);

        // Set up event listeners
        this.player.on('timeupdate', () => {
            this.updateVideoProgress();
        });

        this.player.on('ended', () => {
            this.onVideoEnded();
        });

        this.player.on('error', (error) => {
            this.handleVideoError(error, video);
        });

        this.player.on('loadstart', () => {
            console.log('Video loading started');
        });

        this.player.on('canplay', () => {
            console.log('Video can start playing');
        });

        // Load saved progress
        this.loadSavedProgress(video.id);
    }

    getVideoMimeType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const mimeTypes = {
            'mp4': 'video/mp4',
            'avi': 'video/x-msvideo',
            'mkv': 'video/x-matroska',
            'mov': 'video/quicktime',
            'wmv': 'video/x-ms-wmv',
            'flv': 'video/x-flv',
            'webm': 'video/webm',
            'm4v': 'video/mp4'
        };
        return mimeTypes[extension] || 'video/mp4';
    }

    handleVideoError(error, video) {
        console.error('Video player error:', error);

        // Show user-friendly error message
        const errorMessages = {
            1: 'Video loading was aborted',
            2: 'Network error occurred while loading video',
            3: 'Video format is not supported or file is corrupted',
            4: 'Video source is not available'
        };

        const errorCode = this.player.error()?.code || 0;
        const errorMessage = errorMessages[errorCode] || 'An unknown error occurred while playing the video';

        window.coursePlayer.showToast(`Video Error: ${errorMessage}`, 'error');

        // Try to reload the video source
        if (errorCode === 2 || errorCode === 4) {
            setTimeout(() => {
                console.log('Attempting to reload video...');
                this.player.src({
                    src: `/api/videos/${video.id}/stream`,
                    type: this.getVideoMimeType(video.filename)
                });
            }, 2000);
        }
    }
    
    async loadSavedProgress(videoId) {
        try {
            const response = await fetch(`/api/videos/${videoId}`);
            const videoData = await response.json();

            if (videoData.progress && videoData.progress.current_time > 0) {
                // Resume from saved position (with a small delay to ensure player is ready)
                setTimeout(() => {
                    this.player.currentTime(videoData.progress.current_time);
                }, 1000);
            }
        } catch (error) {
            console.error('Error loading saved progress:', error);
        }
    }

    async updateVideoProgress() {
        if (!this.player || !this.currentVideo) return;

        const currentTime = Math.floor(this.player.currentTime());
        const duration = Math.floor(this.player.duration());
        const completed = currentTime >= duration - 5; // Consider completed if within 5 seconds of end

        try {
            await fetch(`/api/videos/${this.currentVideo.id}/progress`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: this.currentVideo.id,
                    current_time: currentTime,
                    completed: completed
                })
            });

            // Update progress bar in UI
            const progressPercent = (currentTime / duration) * 100;
            const progressBar = document.querySelector(`[data-video-id="${this.currentVideo.id}"]`);
            if (progressBar) {
                progressBar.style.width = `${progressPercent}%`;
            }
        } catch (error) {
            console.error('Error updating video progress:', error);
        }
    }

    onVideoEnded() {
        if (window.coursePlayer.preferences.auto_play_next) {
            this.playNextVideo();
        }
    }

    playPreviousVideo() {
        if (this.currentVideoIndex > 0) {
            const prevVideo = this.videos[this.currentVideoIndex - 1];
            this.playVideo(prevVideo);
        }
    }

    playNextVideo() {
        if (this.currentVideoIndex < this.videos.length - 1) {
            const nextVideo = this.videos[this.currentVideoIndex + 1];
            this.playVideo(nextVideo);
        }
    }

    setPlaybackSpeed(speed) {
        if (this.player) {
            this.player.playbackRate(speed);
        }
    }

    showAddBookmarkModal() {
        if (!this.player || !this.currentVideo) return;

        const currentTime = Math.floor(this.player.currentTime());
        document.getElementById('bookmarkTimestamp').value = window.coursePlayer.formatDuration(currentTime);

        const modal = new bootstrap.Modal(document.getElementById('addBookmarkModal'));
        modal.show();
    }

    showAddNoteModal() {
        if (!this.player || !this.currentVideo) return;

        const currentTime = Math.floor(this.player.currentTime());
        document.getElementById('noteTimestamp').value = window.coursePlayer.formatDuration(currentTime);

        const modal = new bootstrap.Modal(document.getElementById('addNoteModal'));
        modal.show();
    }

    async saveBookmark() {
        const label = document.getElementById('bookmarkLabel').value.trim();
        const description = document.getElementById('bookmarkDescription').value.trim();

        if (!label || !this.currentVideo) return;

        const currentTime = Math.floor(this.player.currentTime());

        try {
            const response = await fetch(`/api/videos/${this.currentVideo.id}/bookmarks`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timestamp: currentTime,
                    label: label,
                    description: description
                })
            });

            if (response.ok) {
                const bookmark = await response.json();
                this.addBookmarkToUI(bookmark);

                // Clear form and close modal
                document.getElementById('bookmarkForm').reset();
                const modal = bootstrap.Modal.getInstance(document.getElementById('addBookmarkModal'));
                modal.hide();

                window.coursePlayer.showToast('Bookmark added successfully', 'success');
            }
        } catch (error) {
            console.error('Error saving bookmark:', error);
            window.coursePlayer.showToast('Error saving bookmark', 'error');
        }
    }

    async saveNote() {
        const content = document.getElementById('noteContent').value.trim();
        const includeTimestamp = document.getElementById('includeTimestamp').checked;

        if (!content || !this.currentVideo) return;

        const currentTime = includeTimestamp ? Math.floor(this.player.currentTime()) : null;

        try {
            const response = await fetch(`/api/videos/${this.currentVideo.id}/notes`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timestamp: currentTime,
                    content: content
                })
            });

            if (response.ok) {
                const note = await response.json();
                this.addNoteToUI(note);

                // Clear form and close modal
                document.getElementById('noteForm').reset();
                const modal = bootstrap.Modal.getInstance(document.getElementById('addNoteModal'));
                modal.hide();

                window.coursePlayer.showToast('Note added successfully', 'success');
            }
        } catch (error) {
            console.error('Error saving note:', error);
            window.coursePlayer.showToast('Error saving note', 'error');
        }
    }

    async loadVideoData(videoId) {
        try {
            const response = await fetch(`/api/videos/${videoId}`);
            const videoData = await response.json();

            this.renderBookmarks(videoData.bookmarks || []);
            this.renderNotes(videoData.notes || []);
        } catch (error) {
            console.error('Error loading video data:', error);
        }
    }

    renderBookmarks(bookmarks) {
        const bookmarksList = document.getElementById('bookmarksList');
        bookmarksList.innerHTML = '';

        if (bookmarks.length === 0) {
            bookmarksList.innerHTML = `
                <div class="list-group-item text-center text-muted">
                    No bookmarks yet
                </div>
            `;
            return;
        }

        bookmarks.forEach(bookmark => {
            this.addBookmarkToUI(bookmark);
        });
    }

    renderNotes(notes) {
        const notesList = document.getElementById('notesList');
        notesList.innerHTML = '';

        if (notes.length === 0) {
            notesList.innerHTML = `
                <div class="list-group-item text-center text-muted">
                    No notes yet
                </div>
            `;
            return;
        }

        notes.forEach(note => {
            this.addNoteToUI(note);
        });
    }

    addBookmarkToUI(bookmark) {
        const bookmarksList = document.getElementById('bookmarksList');

        // Remove "no bookmarks" message if it exists
        const emptyMessage = bookmarksList.querySelector('.text-muted');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        const bookmarkItem = document.createElement('div');
        bookmarkItem.className = 'list-group-item bookmark-item';
        bookmarkItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${bookmark.label}</h6>
                    <p class="mb-1 small">${bookmark.description || ''}</p>
                    <small class="timestamp-badge badge bg-primary">${window.coursePlayer.formatDuration(bookmark.timestamp)}</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="dashboard.deleteBookmark(${bookmark.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        bookmarkItem.addEventListener('click', (e) => {
            if (!e.target.closest('button')) {
                this.player.currentTime(bookmark.timestamp);
            }
        });

        bookmarksList.appendChild(bookmarkItem);
    }

    addNoteToUI(note) {
        const notesList = document.getElementById('notesList');

        // Remove "no notes" message if it exists
        const emptyMessage = notesList.querySelector('.text-muted');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        const noteItem = document.createElement('div');
        noteItem.className = 'list-group-item note-item';
        noteItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <p class="mb-1">${note.content}</p>
                    ${note.timestamp ? `<small class="timestamp-badge badge bg-secondary">${window.coursePlayer.formatDuration(note.timestamp)}</small>` : ''}
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="dashboard.deleteNote(${note.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        if (note.timestamp) {
            noteItem.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    this.player.currentTime(note.timestamp);
                }
            });
        }

        notesList.appendChild(noteItem);
    }

    async deleteBookmark(bookmarkId) {
        try {
            const response = await fetch(`/api/bookmarks/${bookmarkId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                // Remove from UI
                const bookmarkItem = document.querySelector(`button[onclick="dashboard.deleteBookmark(${bookmarkId})"]`).closest('.bookmark-item');
                bookmarkItem.remove();

                // Check if list is empty
                const bookmarksList = document.getElementById('bookmarksList');
                if (bookmarksList.children.length === 0) {
                    bookmarksList.innerHTML = `
                        <div class="list-group-item text-center text-muted">
                            No bookmarks yet
                        </div>
                    `;
                }

                window.coursePlayer.showToast('Bookmark deleted', 'success');
            }
        } catch (error) {
            console.error('Error deleting bookmark:', error);
            window.coursePlayer.showToast('Error deleting bookmark', 'error');
        }
    }

    async deleteNote(noteId) {
        try {
            const response = await fetch(`/api/notes/${noteId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                // Remove from UI
                const noteItem = document.querySelector(`button[onclick="dashboard.deleteNote(${noteId})"]`).closest('.note-item');
                noteItem.remove();

                // Check if list is empty
                const notesList = document.getElementById('notesList');
                if (notesList.children.length === 0) {
                    notesList.innerHTML = `
                        <div class="list-group-item text-center text-muted">
                            No notes yet
                        </div>
                    `;
                }

                window.coursePlayer.showToast('Note deleted', 'success');
            }
        } catch (error) {
            console.error('Error deleting note:', error);
            window.coursePlayer.showToast('Error deleting note', 'error');
        }
    }

    async scanDirectory() {
        const directoryPath = document.getElementById('directoryPath').value.trim();

        if (!directoryPath) {
            window.coursePlayer.showToast('Please enter a directory path', 'error');
            return;
        }

        // Show progress
        document.getElementById('scanProgress').classList.remove('d-none');
        document.getElementById('scanResults').classList.add('d-none');
        document.getElementById('startScan').disabled = true;

        try {
            const response = await fetch('/api/scan-directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: directoryPath
                })
            });

            const result = await response.json();

            // Hide progress and show results
            document.getElementById('scanProgress').classList.add('d-none');
            document.getElementById('scanResults').classList.remove('d-none');

            const resultsList = document.getElementById('scanResultsList');
            resultsList.innerHTML = `
                <li><strong>Courses added:</strong> ${result.courses_added}</li>
                <li><strong>Modules added:</strong> ${result.modules_added}</li>
                <li><strong>Videos added:</strong> ${result.videos_added}</li>
            `;

            if (result.errors && result.errors.length > 0) {
                resultsList.innerHTML += '<li><strong>Errors:</strong><ul>';
                result.errors.forEach(error => {
                    resultsList.innerHTML += `<li class="text-danger">${error}</li>`;
                });
                resultsList.innerHTML += '</ul></li>';
            }

            // Reload courses
            window.coursePlayer.loadCourses();

            window.coursePlayer.showToast('Directory scan completed', 'success');

        } catch (error) {
            console.error('Error scanning directory:', error);
            window.coursePlayer.showToast('Error scanning directory', 'error');

            document.getElementById('scanProgress').classList.add('d-none');
        } finally {
            document.getElementById('startScan').disabled = false;
        }
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();

    // Extend coursePlayer with dashboard methods
    window.coursePlayer.loadCourse = (courseId) => {
        window.dashboard.loadCourse(courseId);
    };
});
