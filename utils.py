import os
import re
from pathlib import Path
from moviepy.editor import Video<PERSON>ileClip
from PIL import Image
import mimetypes
from models import db, Course, Module, Video
from config import Config

def is_video_file(filename):
    """Check if file is a supported video format"""
    ext = Path(filename).suffix.lower()
    return ext in Config.SUPPORTED_VIDEO_FORMATS

def extract_video_metadata(video_path):
    """Extract metadata from video file"""
    try:
        clip = VideoFileClip(video_path)
        duration = int(clip.duration) if clip.duration else 0
        resolution = f"{clip.w}x{clip.h}" if clip.w and clip.h else None
        file_size = os.path.getsize(video_path)
        clip.close()
        
        return {
            'duration': duration,
            'resolution': resolution,
            'file_size': file_size
        }
    except Exception as e:
        print(f"Error extracting metadata from {video_path}: {e}")
        return {
            'duration': 0,
            'resolution': None,
            'file_size': os.path.getsize(video_path) if os.path.exists(video_path) else 0
        }

def generate_thumbnail(video_path, output_path, timestamp=10):
    """Generate thumbnail from video at specified timestamp"""
    try:
        clip = VideoFileClip(video_path)
        # Use timestamp or 10% of video duration, whichever is smaller
        thumb_time = min(timestamp, clip.duration * 0.1) if clip.duration else timestamp
        
        frame = clip.get_frame(thumb_time)
        img = Image.fromarray(frame)
        img.thumbnail(Config.THUMBNAIL_SIZE, Image.Resampling.LANCZOS)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, 'JPEG', quality=85)
        
        clip.close()
        return output_path
    except Exception as e:
        print(f"Error generating thumbnail for {video_path}: {e}")
        return None

def clean_filename(filename):
    """Clean filename for display purposes"""
    # Remove file extension
    name = Path(filename).stem
    
    # Remove common prefixes/patterns
    patterns = [
        r'^\d+[\.\-_\s]*',  # Remove leading numbers
        r'^[Ll]ecture[\s\-_]*\d*[\s\-_]*',  # Remove "Lecture" prefix
        r'^[Cc]hapter[\s\-_]*\d*[\s\-_]*',  # Remove "Chapter" prefix
        r'^[Ss]ection[\s\-_]*\d*[\s\-_]*',  # Remove "Section" prefix
    ]
    
    for pattern in patterns:
        name = re.sub(pattern, '', name)
    
    # Replace underscores and dashes with spaces
    name = re.sub(r'[_\-]+', ' ', name)
    
    # Clean up multiple spaces
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Capitalize first letter of each word
    name = ' '.join(word.capitalize() for word in name.split())
    
    return name or Path(filename).stem

def extract_order_from_filename(filename):
    """Extract order number from filename"""
    # Look for patterns like "01_", "001.", "Lecture 1", etc.
    patterns = [
        r'^(\d+)[\.\-_\s]',
        r'[Ll]ecture[\s\-_]*(\d+)',
        r'[Cc]hapter[\s\-_]*(\d+)',
        r'[Ss]ection[\s\-_]*(\d+)',
        r'[Pp]art[\s\-_]*(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            return int(match.group(1))
    
    return 999  # Default high number for unordered files

def scan_course_directory(directory_path):
    """Scan directory and organize into courses, modules, and videos"""
    directory_path = Path(directory_path)
    
    if not directory_path.exists():
        raise ValueError("Directory does not exist")
    
    results = {
        'courses_added': 0,
        'modules_added': 0,
        'videos_added': 0,
        'errors': []
    }
    
    # Check if this is a single course directory or contains multiple courses
    video_files = list(directory_path.rglob('*'))
    video_files = [f for f in video_files if f.is_file() and is_video_file(f.name)]
    
    if not video_files:
        results['errors'].append("No video files found in directory")
        return results
    
    # Determine structure
    subdirs = [d for d in directory_path.iterdir() if d.is_dir()]
    
    if len(subdirs) == 0:
        # Single course, all videos in root
        course = create_or_update_course(directory_path, directory_path.name)
        if course:
            results['courses_added'] += 1
            module_results = scan_module_directory(directory_path, course.id, "Main")
            results['modules_added'] += module_results['modules_added']
            results['videos_added'] += module_results['videos_added']
            results['errors'].extend(module_results['errors'])
    else:
        # Multiple courses or modules
        for subdir in subdirs:
            if any(f.is_file() and is_video_file(f.name) for f in subdir.rglob('*')):
                # This subdirectory contains videos
                course = create_or_update_course(subdir, subdir.name)
                if course:
                    results['courses_added'] += 1
                    
                    # Check for modules within course
                    course_subdirs = [d for d in subdir.iterdir() if d.is_dir()]
                    if course_subdirs:
                        # Course has modules
                        for module_dir in course_subdirs:
                            module_results = scan_module_directory(module_dir, course.id, module_dir.name)
                            results['modules_added'] += module_results['modules_added']
                            results['videos_added'] += module_results['videos_added']
                            results['errors'].extend(module_results['errors'])
                    else:
                        # Course has videos directly
                        module_results = scan_module_directory(subdir, course.id, "Main")
                        results['modules_added'] += module_results['modules_added']
                        results['videos_added'] += module_results['videos_added']
                        results['errors'].extend(module_results['errors'])
    
    return results

def create_or_update_course(course_path, course_name):
    """Create or update a course in the database"""
    try:
        course_path_str = str(course_path)
        
        # Check if course already exists
        existing_course = Course.query.filter_by(path=course_path_str).first()
        if existing_course:
            return existing_course
        
        course = Course(
            name=clean_filename(course_name),
            path=course_path_str,
            description=f"Course located at {course_path_str}"
        )
        
        db.session.add(course)
        db.session.commit()
        return course
        
    except Exception as e:
        print(f"Error creating course {course_name}: {e}")
        return None

def scan_module_directory(module_path, course_id, module_name):
    """Scan a module directory for videos"""
    results = {
        'modules_added': 0,
        'videos_added': 0,
        'errors': []
    }
    
    try:
        module_path = Path(module_path)
        module_path_str = str(module_path)
        
        # Check if module already exists
        existing_module = Module.query.filter_by(path=module_path_str, course_id=course_id).first()
        if not existing_module:
            module = Module(
                name=clean_filename(module_name),
                path=module_path_str,
                course_id=course_id,
                order_index=extract_order_from_filename(module_name)
            )
            db.session.add(module)
            db.session.commit()
            results['modules_added'] += 1
        else:
            module = existing_module
        
        # Scan for video files
        video_files = [f for f in module_path.iterdir() if f.is_file() and is_video_file(f.name)]
        video_files.sort(key=lambda x: extract_order_from_filename(x.name))
        
        for i, video_file in enumerate(video_files):
            try:
                video_path_str = str(video_file)
                
                # Check if video already exists
                existing_video = Video.query.filter_by(path=video_path_str).first()
                if existing_video:
                    continue
                
                # Extract metadata
                metadata = extract_video_metadata(video_path_str)
                
                # Generate thumbnail
                thumbnail_dir = Path('static/thumbnails')
                thumbnail_dir.mkdir(exist_ok=True)
                thumbnail_filename = f"{module.id}_{video_file.stem}.jpg"
                thumbnail_path = thumbnail_dir / thumbnail_filename
                
                generated_thumbnail = generate_thumbnail(video_path_str, str(thumbnail_path))
                
                video = Video(
                    name=clean_filename(video_file.name),
                    filename=video_file.name,
                    path=video_path_str,
                    module_id=module.id,
                    order_index=extract_order_from_filename(video_file.name),
                    duration=metadata['duration'],
                    file_size=metadata['file_size'],
                    resolution=metadata['resolution'],
                    thumbnail_path=str(thumbnail_path) if generated_thumbnail else None
                )
                
                db.session.add(video)
                results['videos_added'] += 1
                
            except Exception as e:
                results['errors'].append(f"Error processing video {video_file.name}: {str(e)}")
        
        db.session.commit()
        
    except Exception as e:
        results['errors'].append(f"Error scanning module {module_name}: {str(e)}")
    
    return results

def format_duration(seconds):
    """Format duration in seconds to human readable format"""
    if not seconds:
        return "0:00"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def format_file_size(bytes_size):
    """Format file size in bytes to human readable format"""
    if not bytes_size:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024.0
    
    return f"{bytes_size:.1f} PB"
