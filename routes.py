from flask import render_template, request, jsonify, send_file, session, Response, current_app
from models import db, Course, Module, Video, VideoProgress, Bookmark, Note, Tag, VideoTag, UserPreference
import utils
import os
import uuid
import re
from pathlib import Path
import mimetypes
from datetime import datetime

def init_routes(app):
    # Generate session ID for user tracking
    def get_user_session():
        if 'user_session' not in session:
            session['user_session'] = str(uuid.uuid4())
        return session['user_session']

    # Main dashboard route
    @app.route('/')
    def index():
        return render_template('index.html')

    @app.route('/test-video')
    def test_video():
        """Test video player page"""
        return render_template('test_video.html')

    @app.route('/simple-video-test')
    def simple_video_test():
        """Simple HTML5 video test"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Simple Video Test</title>
        </head>
        <body>
            <h1>Simple HTML5 Video Test</h1>
            <video width="800" height="450" controls>
                <source src="/api/videos/1/stream" type="video/mp4">
                Your browser does not support the video tag.
            </video>

            <h2>Direct Link Test</h2>
            <p><a href="/api/videos/1/stream" target="_blank">Direct video link</a></p>

            <h2>Video Info</h2>
            <div id="info"></div>

            <script>
                fetch('/api/videos/1')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('info').innerHTML =
                            '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    })
                    .catch(error => {
                        document.getElementById('info').innerHTML = 'Error: ' + error;
                    });
            </script>
        </body>
        </html>
        '''

    @app.route('/debug-video/<int:video_id>')
    def debug_video(video_id):
        """Debug video information"""
        video = Video.query.get_or_404(video_id)

        debug_info = {
            'video_id': video.id,
            'name': video.name,
            'filename': video.filename,
            'path': video.path,
            'file_exists': os.path.exists(video.path),
            'file_size': video.file_size,
            'duration': video.duration,
            'resolution': video.resolution,
            'stream_url': f'/api/videos/{video.id}/stream',
            'mime_type': None
        }

        # Determine MIME type
        mime_type, _ = mimetypes.guess_type(video.path)
        if not mime_type or not mime_type.startswith('video/'):
            ext = os.path.splitext(video.path)[1].lower()
            mime_type_map = {
                '.mp4': 'video/mp4',
                '.avi': 'video/x-msvideo',
                '.mkv': 'video/x-matroska',
                '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv',
                '.flv': 'video/x-flv',
                '.webm': 'video/webm',
                '.m4v': 'video/mp4'
            }
            mime_type = mime_type_map.get(ext, 'video/mp4')

        debug_info['mime_type'] = mime_type

        # Test file reading
        try:
            with open(video.path, 'rb') as f:
                first_bytes = f.read(100)
                debug_info['first_20_bytes'] = first_bytes[:20].hex()
                debug_info['file_readable'] = True
        except Exception as e:
            debug_info['file_readable'] = False
            debug_info['read_error'] = str(e)

        return jsonify(debug_info)

    @app.route('/video-diagnostics')
    def video_diagnostics():
        """Video diagnostics page"""
        return render_template('video_diagnostics.html')

    # API Routes
    @app.route('/api/courses', methods=['GET'])
    def get_courses():
        """Get all courses"""
        courses = Course.query.all()
        return jsonify([course.to_dict() for course in courses])

    @app.route('/api/courses/<int:course_id>', methods=['GET'])
    def get_course(course_id):
        """Get specific course with modules"""
        course = Course.query.get_or_404(course_id)
        modules = Module.query.filter_by(course_id=course_id).order_by(Module.order_index).all()
        
        course_data = course.to_dict()
        course_data['modules'] = []
        
        for module in modules:
            module_data = module.to_dict()
            videos = Video.query.filter_by(module_id=module.id).order_by(Video.order_index).all()
            module_data['videos'] = [video.to_dict() for video in videos]
            course_data['modules'].append(module_data)
        
        return jsonify(course_data)

    @app.route('/api/videos/<int:video_id>', methods=['GET'])
    def get_video(video_id):
        """Get specific video details"""
        video = Video.query.get_or_404(video_id)
        user_session = get_user_session()
        
        # Get progress
        progress = VideoProgress.query.filter_by(video_id=video_id, user_session=user_session).first()
        
        # Get bookmarks
        bookmarks = Bookmark.query.filter_by(video_id=video_id, user_session=user_session).all()
        
        # Get notes
        notes = Note.query.filter_by(video_id=video_id, user_session=user_session).all()
        
        video_data = video.to_dict()
        video_data['progress'] = progress.to_dict() if progress else None
        video_data['bookmarks'] = [bookmark.to_dict() for bookmark in bookmarks]
        video_data['notes'] = [note.to_dict() for note in notes]
        
        return jsonify(video_data)

    @app.route('/api/videos/<int:video_id>/stream')
    def stream_video(video_id):
        """Stream video file with range request support"""
        video = Video.query.get_or_404(video_id)

        if not os.path.exists(video.path):
            return jsonify({'error': 'Video file not found'}), 404

        # Get file info
        file_size = os.path.getsize(video.path)

        # Determine MIME type based on file extension
        mime_type, _ = mimetypes.guess_type(video.path)
        if not mime_type or not mime_type.startswith('video/'):
            # Fallback MIME types for common video formats
            ext = os.path.splitext(video.path)[1].lower()
            mime_type_map = {
                '.mp4': 'video/mp4',
                '.avi': 'video/x-msvideo',
                '.mkv': 'video/x-matroska',
                '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv',
                '.flv': 'video/x-flv',
                '.webm': 'video/webm',
                '.m4v': 'video/mp4'
            }
            mime_type = mime_type_map.get(ext, 'video/mp4')

        # Handle range requests for video streaming
        range_header = request.headers.get('Range', None)
        if range_header:
            # Parse range header
            byte_start = 0
            byte_end = file_size - 1

            if range_header:
                match = re.search(r'bytes=(\d+)-(\d*)', range_header)
                if match:
                    byte_start = int(match.group(1))
                    if match.group(2):
                        byte_end = int(match.group(2))

            # Ensure valid range
            byte_start = max(0, byte_start)
            byte_end = min(file_size - 1, byte_end)
            content_length = byte_end - byte_start + 1

            def generate_range():
                with open(video.path, 'rb') as f:
                    f.seek(byte_start)
                    remaining = content_length
                    while remaining:
                        chunk_size = min(8192, remaining)  # 8KB chunks
                        data = f.read(chunk_size)
                        if not data:
                            break
                        remaining -= len(data)
                        yield data

            response = Response(
                generate_range(),
                206,  # Partial Content
                headers={
                    'Content-Type': mime_type,
                    'Accept-Ranges': 'bytes',
                    'Content-Range': f'bytes {byte_start}-{byte_end}/{file_size}',
                    'Content-Length': str(content_length),
                    'Cache-Control': 'no-cache'
                }
            )
            return response
        else:
            # Full file request
            def generate_full():
                with open(video.path, 'rb') as f:
                    while True:
                        data = f.read(8192)  # 8KB chunks
                        if not data:
                            break
                        yield data

            response = Response(
                generate_full(),
                headers={
                    'Content-Type': mime_type,
                    'Content-Length': str(file_size),
                    'Accept-Ranges': 'bytes',
                    'Cache-Control': 'no-cache'
                }
            )
            return response

    @app.route('/api/videos/<int:video_id>/progress', methods=['POST'])
    def update_video_progress():
        """Update video progress"""
        video_id = request.json.get('video_id')
        current_time = request.json.get('current_time', 0)
        completed = request.json.get('completed', False)
        user_session = get_user_session()
        
        progress = VideoProgress.query.filter_by(video_id=video_id, user_session=user_session).first()
        
        if not progress:
            progress = VideoProgress(
                video_id=video_id,
                user_session=user_session,
                current_time=current_time,
                completed=completed
            )
            db.session.add(progress)
        else:
            progress.current_time = current_time
            progress.completed = completed
            progress.last_watched = datetime.utcnow()
        
        db.session.commit()
        return jsonify(progress.to_dict())

    @app.route('/api/videos/<int:video_id>/bookmarks', methods=['GET', 'POST'])
    def handle_bookmarks(video_id):
        """Get or create bookmarks for a video"""
        user_session = get_user_session()
        
        if request.method == 'GET':
            bookmarks = Bookmark.query.filter_by(video_id=video_id, user_session=user_session).all()
            return jsonify([bookmark.to_dict() for bookmark in bookmarks])
        
        elif request.method == 'POST':
            data = request.json
            bookmark = Bookmark(
                video_id=video_id,
                user_session=user_session,
                timestamp=data.get('timestamp'),
                label=data.get('label'),
                description=data.get('description', '')
            )
            db.session.add(bookmark)
            db.session.commit()
            return jsonify(bookmark.to_dict()), 201

    @app.route('/api/bookmarks/<int:bookmark_id>', methods=['DELETE'])
    def delete_bookmark(bookmark_id):
        """Delete a bookmark"""
        user_session = get_user_session()
        bookmark = Bookmark.query.filter_by(id=bookmark_id, user_session=user_session).first_or_404()
        
        db.session.delete(bookmark)
        db.session.commit()
        return jsonify({'message': 'Bookmark deleted successfully'})

    @app.route('/api/videos/<int:video_id>/notes', methods=['GET', 'POST'])
    def handle_notes(video_id):
        """Get or create notes for a video"""
        user_session = get_user_session()
        
        if request.method == 'GET':
            notes = Note.query.filter_by(video_id=video_id, user_session=user_session).all()
            return jsonify([note.to_dict() for note in notes])
        
        elif request.method == 'POST':
            data = request.json
            note = Note(
                video_id=video_id,
                user_session=user_session,
                timestamp=data.get('timestamp'),
                content=data.get('content')
            )
            db.session.add(note)
            db.session.commit()
            return jsonify(note.to_dict()), 201

    @app.route('/api/notes/<int:note_id>', methods=['PUT', 'DELETE'])
    def handle_note(note_id):
        """Update or delete a note"""
        user_session = get_user_session()
        note = Note.query.filter_by(id=note_id, user_session=user_session).first_or_404()
        
        if request.method == 'PUT':
            data = request.json
            note.content = data.get('content', note.content)
            note.timestamp = data.get('timestamp', note.timestamp)
            note.updated_at = datetime.utcnow()
            db.session.commit()
            return jsonify(note.to_dict())
        
        elif request.method == 'DELETE':
            db.session.delete(note)
            db.session.commit()
            return jsonify({'message': 'Note deleted successfully'})

    @app.route('/api/search', methods=['GET'])
    def search():
        """Search courses, modules, and videos"""
        query = request.args.get('q', '').strip()
        if not query:
            return jsonify({'courses': [], 'modules': [], 'videos': []})
        
        # Search courses
        courses = Course.query.filter(Course.name.contains(query)).all()
        
        # Search modules
        modules = Module.query.filter(Module.name.contains(query)).all()
        
        # Search videos
        videos = Video.query.filter(Video.name.contains(query)).all()
        
        return jsonify({
            'courses': [course.to_dict() for course in courses],
            'modules': [module.to_dict() for module in modules],
            'videos': [video.to_dict() for video in videos]
        })

    @app.route('/api/scan-directory', methods=['POST'])
    def scan_directory():
        """Scan a directory for courses and videos"""
        data = request.json
        directory_path = data.get('path')
        
        if not directory_path or not os.path.exists(directory_path):
            return jsonify({'error': 'Invalid directory path'}), 400
        
        try:
            result = utils.scan_course_directory(directory_path)
            return jsonify(result)
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/preferences', methods=['GET', 'POST'])
    def handle_preferences():
        """Get or update user preferences"""
        user_session = get_user_session()
        
        if request.method == 'GET':
            prefs = UserPreference.query.filter_by(user_session=user_session).first()
            if not prefs:
                prefs = UserPreference(user_session=user_session)
                db.session.add(prefs)
                db.session.commit()
            return jsonify(prefs.to_dict())
        
        elif request.method == 'POST':
            data = request.json
            prefs = UserPreference.query.filter_by(user_session=user_session).first()
            
            if not prefs:
                prefs = UserPreference(user_session=user_session)
                db.session.add(prefs)
            
            prefs.set_preferences(data)
            prefs.updated_at = datetime.utcnow()
            db.session.commit()
            return jsonify(prefs.to_dict())

    @app.route('/api/tags', methods=['GET', 'POST'])
    def handle_tags():
        """Get all tags or create a new tag"""
        if request.method == 'GET':
            tags = Tag.query.all()
            return jsonify([tag.to_dict() for tag in tags])
        
        elif request.method == 'POST':
            data = request.json
            tag = Tag(
                name=data.get('name'),
                color=data.get('color', '#007bff')
            )
            db.session.add(tag)
            db.session.commit()
            return jsonify(tag.to_dict()), 201

    @app.route('/api/config')
    def get_config():
        """Get application configuration"""
        from config import Config
        return jsonify({
            'supported_video_formats': [fmt.lstrip('.') for fmt in Config.SUPPORTED_VIDEO_FORMATS],
            'thumbnail_size': Config.THUMBNAIL_SIZE,
            'videos_per_page': Config.VIDEOS_PER_PAGE
        })
