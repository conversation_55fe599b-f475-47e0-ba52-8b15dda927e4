# Demo Setup Guide

This guide will help you set up a demo course structure to test the Course Video Player.

## Quick Demo Setup

### 1. Create Sample Directory Structure

```bash
# Create a demo courses directory
mkdir -p ~/demo_courses/Python_Fundamentals/Module_1_Basics
mkdir -p ~/demo_courses/Python_Fundamentals/Module_2_Advanced
mkdir -p ~/demo_courses/Web_Development/HTML_CSS
mkdir -p ~/demo_courses/Web_Development/JavaScript

# You can add your own video files to these directories
# Supported formats: .mp4, .avi, .mkv, .mov, .wmv, .flv, .webm, .m4v
```

### 2. Add Sample Videos (Optional)

If you have video files, copy them to the directories:

```bash
# Example structure:
~/demo_courses/
├── Python_Fundamentals/
│   ├── Module_1_Basics/
│   │   ├── 01_Introduction.mp4
│   │   ├── 02_Variables.mp4
│   │   └── 03_Data_Types.mp4
│   └── Module_2_Advanced/
│       ├── 01_Functions.mp4
│       └── 02_Classes.mp4
└── Web_Development/
    ├── HTML_CSS/
    │   ├── 01_HTML_Basics.mp4
    │   └── 02_CSS_Styling.mp4
    └── JavaScript/
        ├── 01_JS_Fundamentals.mp4
        └── 02_DOM_Manipulation.mp4
```

### 3. Using the Application

1. **Start the application** (if not already running):
   ```bash
   python app.py
   ```

2. **Open in browser**: http://127.0.0.1:5001

3. **Add courses**:
   - Click "Add Course Directory"
   - Enter the path: `/Users/<USER>/demo_courses`
   - Click "Start Scan"

4. **Test features**:
   - Browse courses and modules
   - Play videos (if available)
   - Add bookmarks and notes
   - Try search functionality
   - Toggle dark/light theme
   - Test keyboard shortcuts

## Features to Test

### 🎥 Video Playback
- Play/pause with spacebar
- Seek with arrow keys
- Volume control with up/down arrows
- Speed control buttons (0.5x to 2x)
- Progress tracking and resume

### 📚 Learning Features
- **Bookmarks**: Add timestamped bookmarks during playback
- **Notes**: Add personal notes with optional timestamps
- **Progress**: Automatic progress saving and visual indicators
- **Navigation**: Previous/next video buttons

### 🔍 Organization
- **Search**: Global search across all content
- **Tags**: Add custom tags to videos (future feature)
- **Modules**: Organized by folder structure

### 🎨 User Experience
- **Themes**: Toggle between light and dark modes
- **Responsive**: Works on desktop, tablet, and mobile
- **Settings**: Customize playback preferences

## Troubleshooting Demo

### No Videos Showing?
1. Check that video files are in supported formats
2. Verify directory permissions
3. Check the scan results for errors

### Videos Not Playing?
1. Ensure video files exist and are accessible
2. Check browser console for errors
3. Try different video formats

### Thumbnails Not Generating?
1. Install FFmpeg: `brew install ffmpeg` (macOS)
2. Check that moviepy is working: `python -c "import moviepy; print('OK')"`

## Sample Data

If you don't have video files, the application will still demonstrate:
- Course and module organization
- UI/UX features
- Search functionality
- Settings and preferences
- Theme switching

## API Testing

You can also test the API directly:

```bash
# Get all courses
curl http://127.0.0.1:5001/api/courses

# Search for content
curl "http://127.0.0.1:5001/api/search?q=python"

# Get user preferences
curl http://127.0.0.1:5001/api/preferences
```

## Production Deployment

For production use:

1. **Use a production WSGI server**:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:8000 app:app
   ```

2. **Set environment variables**:
   ```bash
   export SECRET_KEY="your-secret-key"
   export DATABASE_URL="sqlite:///production.db"
   ```

3. **Configure reverse proxy** (nginx/Apache)

4. **Set up SSL/HTTPS** for security

## Next Steps

After testing the demo:

1. **Customize** the UI by editing `static/css/style.css`
2. **Add features** by extending the models and routes
3. **Integrate** with external services (cloud storage, etc.)
4. **Deploy** to a production server
5. **Scale** with PostgreSQL or MySQL for larger datasets

Enjoy exploring your Course Video Player! 🎉
